import {
  Before,
  DataTable,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import {
  DataTestSelector,
  Graphql,
} from '../../../support/helperFunction/mediaDetailHelper';
import '../common/mediaPlayerCommonStep';
import { mediaDetailPage } from '../../../pages/mediaDetail/mediaDetail.po';
import {
  checkMenuOptionsVisibility,
  created2ndMergedGroup,
  createMergedGroup,
  isDetectionUnchecked,
  resizesTheBounding,
  setFileName,
  verifyBlurLevelInputMaximumValue,
  verifyButtonInMergeGroup,
  verifyButtonInRedContainer,
  verifyDetectingObjects,
  verifyDetectionsChecked,
  verifyHeaderIcons,
  verifyObjectIsResized,
  verifyRedactionEffectsDefaults,
  verifyRemoveFromOutputColumn,
  verifyVideoTabObjectList,
  verifyVidPanelHeader,
  warningModalRadioButtonView,
  warningModalView,
  checkProgressIndicator,
} from '../../../pages/mediaDetail/components';

Before(() => {
  cy.LoginLandingPage();
  cy.intercept('POST', Graphql.GraphqlURL, (req) => {
    if (req.body.query === Graphql.JobQuery) {
      req.alias = 'jobStatus';
    }
  });
});

/* When */

When('The user clicks on Notifications icon', () => {
  mediaDetailPage.header.notificationButton().click();
});

When('The user clicks on Keyboard shortcut icon', () => {
  mediaDetailPage.header.shortcutsButton().click();
});

When('The user clicks on Setting icon', () => {
  mediaDetailPage.header.mediaSettingsBtn().click();
});

When('The user clicks on accept icon', () => {
  mediaDetailPage.acceptIcon().click();
});

When(
  'The user clicks on the file name and changes the file name to {string}',
  (fileName: string) => {
    setFileName(fileName);
  }
);

When('The user clicks on deny icon', () => {
  mediaDetailPage.denyIcon().click();
});

When('The user clicks on the status dropdown', () => {
  mediaDetailPage.statusDropdown().click();
});

When('The user selects {string} status', (Status: string) => {
  mediaDetailPage.selectFromDropdown(Status).click();
});

When('The user is on Video Tab', () => {
  mediaDetailPage.videoTabBtn().click();
});

When('The user clicks on Detect Objects button', () => {
  mediaDetailPage.detectObjectButton().click();
});

When('The user selects Head and Objects engine', () => {
  mediaDetailPage.detectOptionDialog().should('be.visible');
  mediaDetailPage.checkBoxOutlineBlankIcon().click();
  mediaDetailPage.detectContinueButton().should('not.be.disabled').click();
});

When('The user clicks on Redaction effects in right panel', () => {
  mediaDetailPage.redactionEffectsDropdown().click();
});

When('The user clicks on the percent next to Head detection', () => {
  mediaDetailPage.headValue().click();
});

When('The user clicks on the percent next to Person detection', () => {
  mediaDetailPage.personValue().click();
});

When('The user clicks on Restore default button', () => {
  mediaDetailPage.restoreDefaultButton().click();
});

When(
  'The user selects code name {string} from dropdown list',
  (codeName: string) => {
    mediaDetailPage.searchRedactionCodeField().clear().type(codeName);
    cy.get('li').first().click();
  }
);

When('The user selects color {string}', (color: string) => {
  cy.get(`[style*="background: ${color}"]`).click();
});

When('The user chooses Restore default color', () => {
  mediaDetailPage.restoreDefaultColorButton().click();
});

When('The user moves the measuring bar to zoom {string}', (percent: string) => {
  mediaDetailPage
    .measuringBarChildren()
    .invoke('attr', 'style', `bottom: ${percent}`)
    .click();
});

When('The user clicks on Close icon', () => {
  mediaDetailPage.mediaDetailCloseBtn().click();
});

When('The user clicks on save button modal', () => {
  mediaDetailPage.saveModalButton().click();
});

When('The user clicks on Filter Detections in right panel', () => {
  mediaDetailPage.rightPanelFilterContainer().within(() => {
    mediaDetailPage.filterDetectionsButton().click();
  });
});

When('The user clicks on Object type', () => {
  mediaDetailPage.objectType().click();
});

When('The user clicks fill redaction', () => {
  mediaDetailPage.redactionType().click();
});

When('The user selects {string}', (selection: string) => {
  cy.get('li').contains(selection).click();
});

When('The user fills blur level with number {string}', (blurLevel: string) => {
  mediaDetailPage.blurLevelInput().clear().type(blurLevel);
});

When('The user clicks Save button', () => {
  mediaDetailPage.saveButtonRedaction().click();
});

When('The user clicks on Confirm button with text {string}', (text: string) => {
  mediaDetailPage.confirmDialogButton().contains(text).click();
});

Then("The user can't fill in number > 10", () =>
  verifyBlurLevelInputMaximumValue()
);

When('The user unchecked Person detection', () => {
  mediaDetailPage.personFilterCheckbox().click();
  cy.get('body').click(0, 0);
});
/* Then */

Then('The header should contain File name {string}', (FileName: string) =>
  mediaDetailPage.fileName().contains(FileName).should('be.visible')
);

Then('The header should contain Editor tab, Redacted Files tab', () => {
  mediaDetailPage.header.editorTab().should('be.visible');
  mediaDetailPage.header.redactedFilesTab().should('be.visible');
});

Then(
  'The header should contain Undo - Redo, Notifications, Keyboard shortcut, Setting, Help center, Close icons',
  () => {
    verifyHeaderIcons();
  }
);

Then(
  'The body should contain Video play screen, Filter Detections, Timeline',
  () => {
    mediaDetailPage.videoPlayScreen().should('be.visible');
    mediaDetailPage.fileDetailTimeline().should('be.visible');
  }
);

Then('The body should contain Video tab, Audio tab, Comments tab', () => {
  mediaDetailPage.resultTabVideo().should('be.visible');
  mediaDetailPage.audioTab().should('be.visible');
  mediaDetailPage.commentsTab().should('be.visible');
});

Then(
  'The footer should contain Preview object in video, Show Unselected toggle',
  () => {
    mediaDetailPage.footer.previewObject().should('be.visible');
    mediaDetailPage.footer.switchShowUnselected().should('be.visible');
  }
);

Then(
  'The footer should contain Last saved: time, Save, Redact file button, Down arrow button',
  () => {
    mediaDetailPage.footer.resultTabSaveBtn().should('be.visible');
    mediaDetailPage.footer.resultTabRedactBtn().should('be.visible');
    mediaDetailPage.footer.expandMoreButton().should('be.visible');
  }
);

Then('The notifications panel is opened', () =>
  mediaDetailPage.footer
    .notificationWindow()
    .should('be.visible')
    .then(() => cy.get('body').click(0, 0))
);

Then('The keyboard shortcuts panel is opened', () => {
  mediaDetailPage.footer.shortcutsHeaderDialog().should('be.visible');

  mediaDetailPage.keyboardShortcutBtn().click();
});

Then('The settings panel is opened', () => {
  mediaDetailPage.footer.globalSettingsModalTitle();
  mediaDetailPage.footer.globalSettingsModalCloseBtn().click();
});

Then('Status should be {string}', (Status: string) => {
  mediaDetailPage.statusDropdown().contains(Status).should('be.visible');
});

Then('Show a dropdown list with statuses', (table: DataTable) => {
  const expectedStatuses = table.hashes().map((row) => {
    const statusName = row.statusName;
    if (!statusName) {
      throw new Error('statusName is required in the data table');
    }
    return statusName;
  });
  expectedStatuses.forEach((status) => {
    mediaDetailPage.selectFromDropdown(status).should('be.visible');
  });
});

Then('Video tab have message {string}', (message: string) => {
  mediaDetailPage.videoMessages().contains(message).should('be.visible');
});

Then('Video tab have the new message', () => {
  verifyDetectingObjects();
  checkProgressIndicator();
  cy.getDataIdCy({ idAlias: 'engine-processing-circular-progress' }).should(
    'not.exist'
  );
});

Then(
  'The Header of Video tab has Detect heads and objects button,Redaction Effects dropdown list, Total types of objects are detected, Filter Detections dropdown list',
  () => {
    verifyVidPanelHeader();
  }
);

Then(
  'The tab has Checkbox to select\\/unselect all objects, Sort by Time, List of objects are detected',
  () => {
    verifyVideoTabObjectList();
  }
);

Then('The tab has list of objects detected', () => {
  mediaDetailPage
    .listOfObjects()
    .should('be.visible')
    .find(`[data-testid=${DataTestSelector.ClusterRow}]`)
    .should('have.length.at.least', 1);
});

Then('The button is disabled and cannot make action', () => {
  mediaDetailPage.detectFaceButton().should('be.disabled').and('exist');
});

Then('Have number percent next to each detection name', () => {
  mediaDetailPage.headValue().invoke('text').should('match', /\d+%/);
  mediaDetailPage.personValue().invoke('text').should('match', /\d+%/);
});

Then('Display a measuring bar', () => {
  mediaDetailPage.measuringBar().should('be.visible');
});

Then('The Media Details Page is closed and return to the Landing page', () => {
  mediaDetailPage.mediaDetailPageContainer().should('not.exist');
  mediaDetailPage.homePage().should('be.visible');
});

Then('Redaction effects pop-up is opened', () => {
  mediaDetailPage.redactionEffectsDialog().should('be.visible');
});

Then(
  'Fill default is blur, Blur level default is 10, Restore default, Cancel and Save button',
  () => {
    verifyRedactionEffectsDefaults();
  }
);

Then('Display a dropdown list containing: {string}', (dataName: string) => {
  const expectedOptions = dataName.split(',').map((item) => item.trim());
  cy.get('#menu-').within(() => {
    checkMenuOptionsVisibility(expectedOptions);
  });
  cy.get('body').click(0, 0);
});

Then(
  'New redaction effect {string} is added to the {string} object type',
  (fill: string, type: string) => {
    mediaDetailPage.objectType().contains(type).should('be.visible');
    mediaDetailPage.redactionType().contains(fill).should('be.visible');
  }
);

Then(
  'The new redaction code {string} is added to the selected object',
  (RedactionCode: string) => {
    mediaDetailPage
      .objectRedaction()
      .children()
      .eq(1)
      .contains(RedactionCode)
      .should('be.visible');
  }
);

Then(
  'The new code color {string} is {string}',
  (code: string, color: string) => {
    mediaDetailPage
      .objectRedaction()
      .contains(code)
      .should('have.css', 'color', color);
  }
);

Then('Open a pop-up {string}', (message: string) => {
  mediaDetailPage.confirmDialogContent().contains(message).should('be.visible');
});

Then('Code {string} is deleted', (Code: string) => {
  mediaDetailPage
    .objectRedaction()
    .children()
    .eq(1)
    .contains(Code)
    .should('not.exist');
});

Then(
  'Display a pop-up with have list of all the detections that are checked',
  () => {
    verifyDetectionsChecked();
  }
);

Then('Have Remove From Output column, default: unselect', () => {
  verifyRemoveFromOutputColumn();
});

When('The user unchecked UDR detection', () => {
  mediaDetailPage.UDRFilterCheckbox().click();
  cy.get('body').click(0, 0);
});

When('The user unchecked Head detection', () => {
  mediaDetailPage.headerFilterCheckbox().click();
  cy.get('body').click(0, 0);
});

Then(
  'The {string} detection is removed from the timeline',
  (detection: string) => {
    mediaDetailPage.timelineRow().contains(detection).should('not.exist');
  }
);

Then(
  'The {string} detection is unchecked in the right panel',
  (detection: string) => {
    isDetectionUnchecked(detection);
  }
);

When('The user remove UDR detection', () => {
  mediaDetailPage.UDRFilterRemoveCheckBox().first().click();
});

Then(
  'Display {string} pop-up- Content: {string}',
  (title: string, content: string) => {
    mediaDetailPage.confirmDialogTitle().contains(title).should('be.visible');
    mediaDetailPage
      .confirmDialogContent()
      .contains(content)
      .should('be.visible');
  }
);

When('The user remove Head detection', () => {
  mediaDetailPage.headFilterRemoveCheckBox().check();
});

When('The user remove Person detection', () => {
  mediaDetailPage.personFilterRemoveCheckBox().check();
});

When('The user resizes the bounding', () => {
  resizesTheBounding();
});

Then('The object during that period is resized', () => {
  verifyObjectIsResized();
});

Then('The deleted person is not displayed in media file', () => {
  mediaDetailPage.objectRedaction().should('not.exist');
});

Then('Selected person group is deleted', () => {
  cy.get('[data-test="detection-name"]')
    .filter((_index, element) => Cypress.$(element).text().trim() === 'PERSON')
    .then(($elements) => {
      const totalElements = $elements.length;
      return cy
        .getKeyValue('totalMatchingDetectionName')
        .then((initialCount: number) =>
          expect(initialCount).to.equal(totalElements + 1)
        );
    });
});

Then('Delete selected segment in the timeline and right panel', () => {
  cy.get('@selectedRowId').then((selectedRowId) =>
    cy.get(`div#${selectedRowId}`).should('not.exist')
  );
});

When('The user created a merged group', () => {
  createMergedGroup();
});

Then(
  'Show warning modal: Header: {string} Body:- {string} - {string}',
  (header: string, body1: string, body2: string) => {
    warningModalView(header, body1, body2);
  }
);

Then('A merged group is created', () => {
  mediaDetailPage.clusterRow().within(() => {
    mediaDetailPage.detectionName().find('span').contains('MERGED GROUP');
  });
});

Then(
  'Button: Cancel in white container, Group,  Merged Group in color {string}',
  (color: string) => {
    verifyButtonInMergeGroup(color);
  }
);

When('The user clicks on {string} button', (confirmBtn: string) => {
  mediaDetailPage
    .confirmDialogButton()
    .filter(
      (_index, el) =>
        el.innerText.trim().toLowerCase() === confirmBtn.toLowerCase()
    )
    .first()
    .click();
});

When('The user cancel the changes', () => {
  mediaDetailPage.cancelButtonModal().click();
});

Then(
  'Show warning modal: Header: {string} Body: - {string} - {string}- 3 radio buttons: Ellipse, Rectangle, Object Type Default Shape - {string}, Button: Cancel in white container, Group, Merged Group in light blue container',
  (header: string, body1: string, body2: string, body3: string) => {
    warningModalRadioButtonView(header, body1, body2, body3);
  }
);

When('The user selects {string} shape', (shape: string) => {
  cy.get('[name="row-radio-buttons-group"]')
    .filter(`[value="${shape}"]`)
    .click();
});

Then(
  'Button: Cancel in white container, Group, Merged Group in red container',
  () => {
    verifyButtonInRedContainer();
  }
);

Then('The merged group is deleted', () => {
  mediaDetailPage.objectRedaction().should('not.exist');
});

When('The user created a 2nd merged group', () => {
  created2ndMergedGroup();
});
